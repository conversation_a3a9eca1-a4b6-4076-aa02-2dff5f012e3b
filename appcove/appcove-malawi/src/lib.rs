pub mod web;
pub mod api;

pub trait App: approck::server::App + approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        self.is_logged_in()
    }
    fn api_usage(&self) -> bool {
        self.is_logged_in()
    }
    fn guard_add(&self) -> bool {
        true
    }
    fn guard_read(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn guard_list(&self) -> bool {
        true
    }
    fn guard_edit(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn guard_delete(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn guard_edit_confirm(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn guard_delete_confirm(&self, _guard_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_add(&self) -> bool {
        true
    }
    fn schedule_read(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_list(&self) -> bool {
        true
    }
    fn attendance_add(&self) -> bool {
        true
    }
    fn attendance_read(&self, _guard_attendance_uuid: granite::Uuid) -> bool {
        true
    }
    fn attendance_list(&self) -> bool {
        true
    }
    fn schedule_edit(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_edit_confirm(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn schedule_delete(&self, _guard_schedule_uuid: granite::Uuid) -> bool {
        true
    }
    fn attendance_delete(&self, _guard_attendance_uuid: granite::Uuid) -> bool {
        true
    }
    fn attendance_edit(&self, _guard_attendance_uuid: granite::Uuid) -> bool {
        true
    }
    fn attendance_edit_confirm(&self, _guard_attendance_uuid: granite::Uuid) -> bool {
        true
    }
    fn wkval_add(&self) -> bool {
        true
    }
    fn wkval_edit(&self) -> bool {
        true
    }
    fn wkval_delete(&self) -> bool {
        true
    }
    fn wkval_read(&self, _wkval_uuid: granite::Uuid) -> bool {
        true
    }
    fn wkval_list(&self) -> bool {
        true
    }
    fn status_add(&self) -> bool {
        true
    }
    fn status_list(&self) -> bool {
        true
    }
    fn manager_add(&self) -> bool {
        true
    }
    fn manager_list(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base + bux::document::PageNav + auth_fence::Document {}

pub fn ml_guard(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/guard/{guard_uuid}/")
}
pub fn ml_guard_edit(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/guard/{guard_uuid}/edit")
}

pub fn ml_guard_delete(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/guard/{guard_uuid}/delete")
}

pub fn ml_guard_edit_confirm(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/guard/{guard_uuid}/edit/confirm")
}

pub fn ml_guard_delete_confirm(guard_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/guard/{guard_uuid}/delete/confirm")
}

pub fn ml_guard_list() -> String {
    "/malawi/guardtr/guard/".to_string()
}

pub fn ml_guard_add() -> String {
    "/malawi/guardtr/guard/add/".to_string()
}

pub fn ml_guard_schedule(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/schedule/{guard_schedule_uuid}/")
}

pub fn ml_guard_attendance(guard_attendance_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/attendance/{guard_attendance_uuid}/")
}

pub fn ml_attendance_list() -> String {
    "/malawi/guardtr/attendance/".to_string()
}

pub fn ml_attendance_add() -> String {
    "/malawi/guardtr/attendance/add/".to_string()
}

pub fn ml_schedule_list() -> String {
    "/malawi/guardtr/schedule/".to_string()
}

pub fn ml_schedule_add() -> String {
    "/malawi/guardtr/schedule/add/".to_string()
}

pub fn ml_schedule_edit(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/schedule/{guard_schedule_uuid}/edit")
}

pub fn ml_schedule_edit_confirm(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/schedule/{guard_schedule_uuid}/edit/confirm")
}

pub fn ml_schedule_delete(guard_schedule_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/schedule/{guard_schedule_uuid}/delete")
}

pub fn ml_attendance_edit(guard_attendance_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/attendance/{guard_attendance_uuid}/edit")
}

pub fn ml_attendance_edit_confirm(guard_attendance_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/attendance/{guard_attendance_uuid}/edit/confirm")
}
pub fn ml_attendance_delete(guard_attendance_uuid: granite::Uuid) -> String {
    format!("/malawi/guardtr/attendance/{guard_attendance_uuid}/delete")
}

pub fn ml_wkval(wkval_uuid: granite::Uuid) -> String {
    format!("/malawi/wkval/{wkval_uuid}/")
}

pub fn ml_wkval_edit(wkval_uuid: granite::Uuid) -> String {
    format!("/malawi/wkval/{wkval_uuid}/edit")
}
pub fn ml_wkval_delete(wkval_uuid: granite::Uuid) -> String {
    format!("/malawi/wkval/{wkval_uuid}/delete")
}
pub fn ml_wkval_list() -> String {
    "/malawi/wkval/".to_string()
}
pub fn ml_wkval_add() -> String {
    "/malawi/wkval/add/".to_string()
}
pub fn ml_status(status_uuid: granite::Uuid) -> String {
    format!("/malawi/settings/status/{status_uuid}/")
}
pub fn ml_settings() -> String {
    "/malawi/settings/".to_string()
}
