#[approck::http(GET /malawi/wkval/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document, qs: QueryString) -> Result<Response> {
        use maud::html;

        // TODO: Figure out how to add a button to the page nav
        doc.page_nav_add_record("Add New Weekly Value", &crate::ml_wkval_add());
        doc.set_title("Weekly Values");

        use crate::web::malawi::wkval::index::wkval_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = wkval_list::call(
            app,
            identity,
            wkval_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Create a detail table with wkval directly in the constructor
        let mut dt = bux::component::detail_table(output.wkval_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_name_column(|a| &a.name);
        dt.add_column("Manager", |a| html! { (a.manager_name) });
        dt.add_column("Due Date", |a| html! { (a.due_by_date.format("%B %d, %Y")) });
        dt.add_active_status_column("Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_wkval(a.wkval_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod wkval_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub wkval_list: Vec<Wkval>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Wkval {
        pub wkval_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub manager_uuid: Uuid,
        pub manager_name: String,
        pub start_date: DateUtc,
        pub due_by_date: DateUtc,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.wkval_list() {
            return_authorization_error!("insufficient permissions to wkval list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                wkval_uuid: Uuid,
                create_ts: DateTimeUtc,
                name: String,
                manager_uuid: Uuid,
                manager_name: String,
                start_date: DateUtc,
                due_by_date: DateUtc,
                active: bool,
            };
            SELECT
                w.wkval_uuid,
                w.create_ts,
                w.name,
                w.manager_uuid,
                m.identity_name as manager_name,
                w.start_date,
                w.due_by_date,
                w.active
            FROM
                appcove_malawi.wkval w
            LEFT JOIN
                appcove_malawi.manager m ON w.manager_uuid = m.manager_uuid
            WHERE true
                AND ($keyword::text IS NULL OR (w.name ILIKE "%" || $keyword::text || "%" OR m.identity_name ILIKE "%" || $keyword::text || "%"))
                AND ($active::bool IS NULL OR w.active = $active::bool)
            ORDER BY
                w.start_date DESC,
                w.name
        )
        .await?;

        Ok(Output {
            wkval_list: rows
                .into_iter()
                .map(|r| Wkval {
                    wkval_uuid: r.wkval_uuid,
                    create_ts: r.create_ts,
                    name: r.name,
                    manager_uuid: r.manager_uuid,
                    manager_name: r.manager_name,
                    start_date: r.start_date,
                    due_by_date: r.due_by_date,
                    active: r.active,
                })
                .collect(),
        })
    }
}
