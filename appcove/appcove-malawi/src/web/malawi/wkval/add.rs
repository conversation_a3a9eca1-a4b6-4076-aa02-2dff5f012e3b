#[approck::http(GET /malawi/wkval/add/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        use crate::api::malawi::settings::status::status_list::status_list;
        use crate::api::malawi::settings::manager::manager_list::manager_list;
        let status_list = status_list::call(
            app,
            identity,
            status_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        let manager_list = manager_list::call(
            app,
            identity,
            manager_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        doc.set_title("Weekly Value Add");

        let mut form_panel = bux::component::add_cancel_form_panel("Add Weekly Value", "/malawi/wkval/");

        let status_options: Vec<(String, String)> = status_list
            .status_list
            .iter()
            .map(|s| (s.status_uuid.to_string(), s.name.clone()))
            .collect();

        let status_options_ref: Vec<(&str, &str)> = status_options
            .iter()
            .map(|(uuid, name)| (uuid.as_str(), name.as_str()))
            .collect();

        let manager_options: Vec<(String, String)> = manager_list
            .manager_list
            .iter()
            .map(|m| (m.manager_uuid.to_string(), m.identity_name.clone()))
            .collect();

        let manager_options_ref: Vec<(&str, &str)> = manager_options
            .iter()
            .map(|(uuid, name)| (uuid.as_str(), name.as_str()))
            .collect();

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::date::bux_input_date("start_date", "Start Date", None))
            (bux::input::date::bux_input_date("due_by_date", "Due By Date", None))
            (bux::input::text::string::name_label_value("name", "Name", None))
            (bux::input::textarea::string::name_label_value("description", "Description", None))
            (bux::input::text::string::name_label_value("pm5_url", "PM5 URL", None))
            (bux::input::select::nilla::nilla_select("status_uuid", "Status", &status_options_ref, None))
            (bux::input::select::nilla::nilla_select("manager_uuid", "Manager", &manager_options_ref, None))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod wkval_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub start_date: DateUtc,
        pub due_by_date: DateUtc,
        pub name: String,
        pub description: Option<String>,
        pub pm5_url: Option<String>,
        pub status_uuid: Uuid,
        pub manager_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub wkval_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.wkval_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to add weekly value".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $start_date: &input.start_date,
                    $due_by_date: &input.due_by_date,
                    $name: &input.name,
                    $description: &input.description,
                    $pm5_url: &input.pm5_url,
                    $status_uuid: &input.status_uuid,
                    $manager_uuid: &input.manager_uuid,
                };
                row = {
                    wkval_uuid: Uuid,
                };

                INSERT INTO
                    appcove_malawi.wkval
                    (
                        start_date,
                        due_by_date,
                        name,
                        description,
                        pm5_url,
                        status_uuid,
                        manager_uuid
                    )
                VALUES
                    (
                        $start_date,
                        $due_by_date,
                        $name,
                        $description,
                        $pm5_url,
                        $status_uuid,
                        $manager_uuid
                    )
                RETURNING
                    wkval_uuid
            )
            .await?;

            dbtx.commit().await.amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            wkval_uuid: row.wkval_uuid,
            detail_url: crate::ml_wkval(row.wkval_uuid),
        }))
    }
}
