#[approck::http(GET /malawi/wkval/{wkval_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document, path: Path) -> Result<Response> {
        use crate::web::malawi::wkval::mapper::index::wkval_detail;
        use maud::html;

        // Get wkval details
        let wkval = wkval_detail::call(
            app,
            identity,
            wkval_detail::Input {
                wkval_uuid: path.wkval_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Weekly Value");

        let title = format!("Delete Weekly Value: {}?", wkval.name);

        let mut panel = bux::component::delete_cancel_form_panel(&title, &crate::ml_wkval_list());
        panel.set_hidden("wkval_uuid", path.wkval_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the weekly value "
                strong { (wkval.name) }
                " managed by " strong { (wkval.manager_name) }
                " from " (wkval.start_date.format("%Y-%m-%d"))
                " to " (wkval.due_by_date.format("%Y-%m-%d")) ". "
                "The weekly value will no longer appear in active lists, but all historical data will be preserved."
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this weekly value.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod wkval_delete {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub wkval_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.wkval_delete() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete weekly value".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $wkval_uuid: &input.wkval_uuid,
            };
            UPDATE
                appcove_malawi.wkval
            SET
                active = false
            WHERE
                wkval_uuid = $wkval_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_wkval_list(),
            message: "Weekly value deleted".into(),
        }))
    }
}
