#[approck::http(GET /malawi/wkval/{wkval_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document, path: Path) -> Result<Response> {
        use crate::web::malawi::wkval::mapper::index::wkval_detail;
        use crate::api::malawi::settings::manager::manager_list::manager_list;
        use crate::api::malawi::settings::status::status_list::status_list;
        use maud::html;

        doc.set_title("Edit Weekly Value");

        let mut form_panel = bux::component::save_cancel_form_panel("Edit Weekly Value", "/malawi/wkval/");

        // Get wkval details
        let wkval = wkval_detail::call(
            app,
            identity,
            wkval_detail::Input {
                wkval_uuid: path.wkval_uuid,
            },
        )
        .await?;

        // Get status list for the dropdown
        let status_list = status_list::call(
            app,
            identity,
            status_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Get manager list for the dropdown
        let manager_list = manager_list::call(
            app,
            identity,
            manager_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        form_panel.set_hidden("wkval_uuid", wkval.wkval_uuid.to_string());

        let status_options: Vec<(String, String)> = status_list
            .status_list
            .iter()
            .map(|s| (s.status_uuid.to_string(), s.name.clone()))
            .collect();

        let status_options_ref: Vec<(&str, &str)> = status_options
            .iter()
            .map(|(uuid, name)| (uuid.as_str(), name.as_str()))
            .collect();

        let manager_options: Vec<(String, String)> = manager_list
            .manager_list
            .iter()
            .map(|m| (m.manager_uuid.to_string(), m.identity_name.clone()))
            .collect();

        let manager_options_ref: Vec<(&str, &str)> = manager_options
            .iter()
            .map(|(uuid, name)| (uuid.as_str(), name.as_str()))
            .collect();

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::date::bux_input_date("start_date", "Start Date", Some(wkval.start_date)))
            (bux::input::date::bux_input_date("due_by_date", "Due By Date", Some(wkval.due_by_date)))
            (bux::input::text::string::name_label_value("name", "Name", Some(&wkval.name)))
            (bux::input::textarea::string::name_label_value("description", "Description", wkval.description.as_deref()))
            (bux::input::text::string::name_label_value("pm5_url", "PM5 URL", wkval.pm5_url.as_deref()))
            (bux::input::select::nilla::nilla_select("status_uuid", "Status", &status_options_ref, wkval.status_uuid.as_ref().map(|u| u.to_string()).as_deref()))
            (bux::input::select::nilla::nilla_select("manager_uuid", "Manager", &manager_options_ref, wkval.manager_uuid.as_ref().map(|u| u.to_string()).as_deref()))
            (bux::input::textarea::string::name_label_value("accomplishments", "Accomplishments", wkval.accomplishments.as_deref()))
            (bux::input::text::string::name_label_value("percent_complete", "Percent Complete", wkval.percent_complete.as_ref().map(|p| p.to_string()).as_deref()))
            (bux::input::date::bux_input_date("requirements_delivered_date", "Requirements Delivered Date", wkval.requirements_delivered_date))
            (bux::input::date::bux_input_date("requirements_reviewed_date", "Requirements Reviewed Date", wkval.requirements_reviewed_date))
            (bux::input::date::bux_input_date("value_delivered_date", "Value Delivered Date", wkval.value_delivered_date))
            (bux::input::date::bux_input_date("value_reviewed_date", "Value Reviewed Date", wkval.value_reviewed_date))
            (bux::input::select::nilla::nilla_select("value_reviewed_by", "Value Reviewed By", &manager_options_ref, wkval.value_reviewed_by.as_ref().map(|u| u.to_string()).as_deref()))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod wkval_edit {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub wkval_uuid: Uuid,
        pub start_date: DateUtc,
        pub due_by_date: DateUtc,
        pub name: String,
        pub description: Option<String>,
        pub pm5_url: Option<String>,
        pub status_uuid: Option<Uuid>,
        pub manager_uuid: Option<Uuid>,
        pub accomplishments: Option<String>,
        pub percent_complete: Option<i32>,
        pub requirements_delivered_date: Option<DateUtc>,
        pub requirements_reviewed_date: Option<DateUtc>,
        pub value_delivered_date: Option<DateUtc>,
        pub value_reviewed_date: Option<DateUtc>,
        pub value_reviewed_by: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub wkval_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.as_ref().wkval_edit() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit weekly value".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $wkval_uuid: &input.wkval_uuid,
                    $start_date: &input.start_date,
                    $due_by_date: &input.due_by_date,
                    $name: &input.name,
                    $description: &input.description,
                    $pm5_url: &input.pm5_url,
                    $status_uuid: &input.status_uuid,
                    $manager_uuid: &input.manager_uuid,
                    $accomplishments: &input.accomplishments,
                    $percent_complete: &input.percent_complete,
                    $requirements_delivered_date: &input.requirements_delivered_date,
                    $requirements_reviewed_date: &input.requirements_reviewed_date,
                    $value_delivered_date: &input.value_delivered_date,
                    $value_reviewed_date: &input.value_reviewed_date,
                    $value_reviewed_by: &input.value_reviewed_by,
                };
                row = {
                    wkval_uuid: Uuid,
                };

                UPDATE
                    appcove_malawi.wkval
                SET
                    start_date = $start_date,
                    due_by_date = $due_by_date,
                    name = $name,
                    description = $description,
                    pm5_url = $pm5_url,
                    status_uuid = $status_uuid,
                    manager_uuid = $manager_uuid,
                    accomplishments = $accomplishments,
                    percent_complete = $percent_complete,
                    requirements_delivered_date = $requirements_delivered_date,
                    requirements_reviewed_date = $requirements_reviewed_date,
                    value_delivered_date = $value_delivered_date,
                    value_reviewed_date = $value_reviewed_date,
                    value_reviewed_by = $value_reviewed_by
                WHERE
                    wkval_uuid = $wkval_uuid::uuid
                RETURNING
                    wkval_uuid
            )
            .await?;

            dbtx.commit().await.amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            wkval_uuid: row.wkval_uuid,
            detail_url: crate::ml_wkval(row.wkval_uuid),
        }))
    }
}

