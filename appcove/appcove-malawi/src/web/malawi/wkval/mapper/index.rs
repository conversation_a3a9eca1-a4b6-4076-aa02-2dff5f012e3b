#[approck::http(GET /malawi/wkval/{wkval_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document, path: Path) -> Result<Response> {
        use crate::web::malawi::wkval::mapper::index::wkval_detail;
        use maud::html;

        let wkval = wkval_detail::call(
            app,
            identity,
            wkval_detail::Input {
                wkval_uuid: path.wkval_uuid,
            },
        )
        .await?;
        doc.page_nav_edit_record("Edit Weekly Value", &crate::ml_wkval_edit(wkval.wkval_uuid));
        doc.page_nav_delete_record("Delete Weekly Value", &crate::ml_wkval_delete(wkval.wkval_uuid));

        // Weekly Value Information
        let wkval_info = {
            let mut wkval_info = bux::component::insight_deck::InsightDeck::new("Weekly Value Information");
            wkval_info.description("An overview of the weekly value details.");

            wkval_info.add_basic_row("fas fa-tag", "Name", html!((wkval.name)));

            wkval_info.add_basic_row(
                "fas fa-calendar-alt",
                "Start Date",
                html!((wkval.start_date.format("%B %d, %Y"))),
            );

            wkval_info.add_basic_row(
                "fas fa-calendar-check",
                "Due By Date",
                html!((wkval.due_by_date.format("%B %d, %Y"))),
            );

            wkval_info.add_basic_row("fas fa-user-tie", "Manager", html!((wkval.manager_name)));

            if let Some(pm5_url) = &wkval.pm5_url {
                wkval_info.add_basic_row(
                    "fas fa-link",
                    "PM5 URL",
                    html! {
                        a href=(pm5_url) target="_blank" { (pm5_url) }
                    },
                );
            }

            wkval_info
        };

        // Description Panel (if description exists)
        let description_panel = wkval.description.as_ref().map(|description| {
            html! {
                panel.description-panel {
                    header {
                        h5 { "Description" }
                    }
                    content {
                        div.description-content {
                            (description)
                        }
                    }
                }
            }
        });

        doc.add_body(html!(
            insight-deck #malawi-wkval-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (wkval.name) }
                                    p.manager.mb-0 {
                                        "Manager: " (wkval.manager_name)
                                    }
                                    p.dates {
                                        "Start: " (wkval.start_date.format("%m/%d/%Y"))
                                        br;
                                        "Due: " (wkval.due_by_date.format("%m/%d/%Y"))
                                    }
                                    hr;
                                    @if let Some(pm5_url) = &wkval.pm5_url {
                                        p.pm5-link {
                                            a href=(pm5_url) target="_blank" { "View PM5" }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        (wkval_info)
                        @if let Some(desc_panel) = description_panel {
                            (desc_panel)
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod wkval_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub wkval_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub wkval_uuid: Uuid,
        pub start_date: DateUtc,
        pub due_by_date: DateUtc,
        pub name: String,
        pub description: Option<String>,
        pub pm5_url: Option<String>,
        pub manager_uuid: Uuid,
        pub manager_name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.wkval_read(input.wkval_uuid) {
            return_authorization_error!("insufficient permissions to read weekly value");
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $wkval_uuid: &input.wkval_uuid,
            };
            row = {
                wkval_uuid: Uuid,
                start_date: DateUtc,
                due_by_date: DateUtc,
                name: String,
                description: Option<String>,
                pm5_url: Option<String>,
                manager_uuid: Uuid,
                manager_name: String,
            };
            SELECT
                w.wkval_uuid,
                w.start_date,
                w.due_by_date,
                w.name,
                w.description,
                w.pm5_url,
                w.manager_uuid,
                m.identity_name as manager_name,
                w.status_uuid,
                s.name as status_name,
                w.accomplishments,
                w.percent_complete,
                w.requirements_delivered_date,
                w.requirements_reviewed_date,
                w.value_delivered_date,
                w.value_reviewed_date,
                w.value_reviewed_by,
                rb.identity_name as value_reviewed_by_name
            FROM
                appcove_malawi.wkval w
            LEFT JOIN
                appcove_malawi.manager m ON w.manager_uuid = m.manager_uuid
            LEFT JOIN
                appcove_malawi.status s ON w.status_uuid = s.status_uuid
            LEFT JOIN
                appcove_malawi.manager rb ON w.value_reviewed_by = rb.manager_uuid
            WHERE true
                AND w.wkval_uuid = $wkval_uuid::uuid
        )
        .await?;

        Ok(Output {
            wkval_uuid: row.wkval_uuid,
            start_date: row.start_date,
            due_by_date: row.due_by_date,
            name: row.name,
            description: row.description,
            pm5_url: row.pm5_url,
            manager_uuid: row.manager_uuid,
            manager_name: row.manager_name,
            status_uuid: row.status_uuid,
            status_name: row.status_name,
            accomplishments: row.accomplishments,
            percent_complete: row.percent_complete,
            requirements_delivered_date: row.requirements_delivered_date,
            requirements_reviewed_date: row.requirements_reviewed_date,
            value_delivered_date: row.value_delivered_date,
            value_reviewed_date: row.value_reviewed_date,
            value_reviewed_by: row.value_reviewed_by,
            value_reviewed_by_name: row.value_reviewed_by_name,
        })
    }
}
