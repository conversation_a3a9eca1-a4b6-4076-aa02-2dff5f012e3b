import "./edit.mcss";
import "@bux/input/date.mts";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { wkval_edit } from "./editλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputDate from "@bux/input/date.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $wkval_uuid: HTMLInputElement = SE($form, "[name=wkval_uuid]");
const $start_date: BuxInputDate = SE($form, "[name=start_date]");
const $due_by_date: BuxInputDate = SE($form, "[name=due_by_date]");
const $name: BuxInputTextString = SE($form, "[name=name]");
const $description: BuxInputTextareaString = SE($form, "[name=description]");
const $pm5_url: BuxInputTextString = SE($form, "[name=pm5_url]");
const $manager_uuid: HTMLSelectElement = SE($form, "[name=manager_uuid]");

new FormPanel({
    $form,
    api: wkval_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $start_date.set_e(errors.start_date);
        $due_by_date.set_e(errors.due_by_date);
        $name.set_e(errors.name);
        $description.set_e(errors.description);
        $pm5_url.set_e(errors.pm5_url);
        // Note: manager_uuid is a select element, no set_e method needed
    },

    get: () => {
        return {
            wkval_uuid: $wkval_uuid.value,
            start_date: $start_date.value || undefined,
            due_by_date: $due_by_date.value || undefined,
            name: $name.value,
            description: $description.value_option,
            pm5_url: $pm5_url.value_option,
            manager_uuid: $manager_uuid.value,
        };
    },

    set: (_value) => {
        // No need to set values for add form
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
