import FormPanel from "@bux/component/form_panel.mts";

const form_panel = new FormPanel({
    get: {
        wkval_uuid: () => (document.querySelector('input[name="wkval_uuid"]') as HTMLInputElement)?.value || "",
        status: () => (document.querySelector('select[name="status"]') as HTMLSelectElement)?.value || "",
        description: () => (document.querySelector('textarea[name="description"]') as HTMLTextAreaElement)?.value || "",
    },
    set: {
        wkval_uuid: (value: string) => {
            const input = document.querySelector('input[name="wkval_uuid"]') as HTMLInputElement;
            if (input) input.value = value;
        },
        status: (value: string) => {
            const select = document.querySelector('select[name="status"]') as HTMLSelectElement;
            if (select) select.value = value;
        },
        description: (value: string) => {
            const textarea = document.querySelector('textarea[name="description"]') as HTMLTextAreaElement;
            if (textarea) textarea.value = value;
        },
    },
    err: {
        wkval_uuid: (message: string) => console.error("wkval_uuid error:", message),
        status: (message: string) => console.error("status error:", message),
        description: (message: string) => console.error("description error:", message),
    },
});
