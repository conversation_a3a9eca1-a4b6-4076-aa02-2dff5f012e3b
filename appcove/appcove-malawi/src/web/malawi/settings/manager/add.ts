import "./add.mcss";
import "@bux/input/select/nilla.mts";

import { SE } from "@granite/lib.mts";
import { manager_add } from "./addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $identity_uuid: BuxInputSelectNilla<string> = SE($form, "[name=identity_uuid]");

new FormPanel({
    $form,
    api: manager_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $identity_uuid.set_e(errors.identity_uuid);
    },

    get: () => {
        // Get the selected option's text for identity_name
        const selectElement = $identity_uuid.querySelector('select') as HTMLSelectElement;
        const selectedOption = selectElement.options[selectElement.selectedIndex];
        const identity_name = selectedOption ? selectedOption.textContent || '' : '';

        return {
            identity_uuid: $identity_uuid.value,
            identity_name: identity_name,
        };
    },

    set: (_value) => {
        // No need to set values for add form
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});