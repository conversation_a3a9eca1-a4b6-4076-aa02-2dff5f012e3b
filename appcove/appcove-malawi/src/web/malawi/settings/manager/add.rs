#[approck::http(GET /malawi/settings/manager/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        use auth_fence::api::admin::identity::list::list;
        let identity_list = list::call(app, identity, list::Input {
            keyword: None,
            active: Some(true),
        })
        .await?;

        doc.set_title("Add Manager");

        let mut form_panel = bux::component::add_cancel_form_panel("Add Manager", "/malawi/settings/");

        // Format identities for dropdown: (uuid, name)
        let identity_options_strings: Vec<(String, String)> = identity_list
            .identities
            .iter()
            .map(|i| (i.identity_uuid.to_string(), i.name.clone()))
            .collect();

        // Convert to the expected type for nilla_select
        let identity_options: Vec<(&str, &str)> = identity_options_strings
            .iter()
            .map(|(uuid, name)| (uuid.as_str(), name.as_str()))
            .collect();

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::select::nilla::nilla_select("identity_uuid", "Identity", &identity_options, None))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}


#[approck::api]
pub mod manager_add {
    use granite::{ResultExt, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub identity_name: String,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub manager_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.manager_add() {
            return_authorization_error!("insufficient permissions to add manager");
        }

        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                    $identity_name: &input.identity_name,
                };
                row = {
                    manager_uuid: Uuid,
                };

                INSERT INTO
                    appcove_malawi.manager
                    (
                        identity_name,
                        identity_uuid
                    )
                VALUES
                    (
                        $identity_name,
                        $identity_uuid
                    )
                RETURNING
                    manager_uuid
            )
            .await?;

            dbtx.commit().await.amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Output {
            manager_uuid: row.manager_uuid,
            detail_url: crate::ml_settings(),
        })
    }
}
