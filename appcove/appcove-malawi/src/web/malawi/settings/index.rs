#[approck::http(GET /malawi/settings/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        use crate::api::malawi::settings::status::status_list::status_list;
        let status_list = status_list::call(
            app,
            identity,
            status_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        doc.set_title("Settings");

        doc.add_body(html! {
            h1 { "Settings" }
        });


        // List the status types
        // Add a button to add a new status type

        doc.add_body(html! {
            a href="/malawi/settings/status/add" { "Add Status" }
        });

        doc.add_body(html! {
            ul {
                @for status in &status_list.status_list {
                    li { (status.name) }
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

