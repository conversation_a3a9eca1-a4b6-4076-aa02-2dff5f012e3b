#[approck::api]
pub mod manager_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub manager_list: Vec<Manager>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Manager {
        pub manager_uuid: Uuid,
        pub identity_uuid: Uuid,
        pub identity_name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.manager_list() {
            return_authorization_error!("insufficient permissions to manager list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                manager_uuid: Uuid,
                identity_uuid: Uuid,
                identity_name: String,
            };
            SELECT
                manager_uuid,
                identity_uuid,
                identity_name
            FROM
                appcove_malawi.manager
            WHERE true
                AND ($keyword::text IS NULL OR identity_name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                identity_name
        )
        .await?;

        Ok(Output {
            manager_list: rows
                .into_iter()
                .map(|r| Manager {
                    manager_uuid: r.manager_uuid,
                    identity_uuid: r.identity_uuid,
                    identity_name: r.identity_name,
                })
                .collect(),
        })
    }
}
