#[approck::api]
pub mod status_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub status_list: Vec<Status>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Status {
        pub status_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.status_list() {
            return_authorization_error!("insufficient permissions to status list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                status_uuid: Uuid,
                create_ts: DateTimeUtc,
                name: String,
                active: bool,
            };
            SELECT
                status_uuid,
                create_ts,
                name,
                active
            FROM
                appcove_malawi.status
            WHERE true
                AND ($keyword::text IS NULL OR name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                name
        )
        .await?;

        Ok(Output {
            status_list: rows
                .into_iter()
                .map(|r| Status {
                    status_uuid: r.status_uuid,
                    create_ts: r.create_ts,
                    name: r.name,
                    active: r.active,
                })
                .collect(),
        })
    }
}
