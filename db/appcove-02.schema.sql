/*
We need an actual accomplishments and % complete field added to close out the week.
And some other fields like when requirements were delivered  and reviewed and when 
the value was delivered and reviewd and by whom.

Accomplishments: text
% Complete: int
requirements_delivered_and_approved_date: date

value_delivered_date: date
value_reviewed_date: date
value_reviewed_by: uuid

The manager field should be a uuid to identity
*/

--create a table for managers that contains the identity_uuid and name
CREATE TABLE appcove_malawi.manager (
    manager_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    identity_uuid uuid NOT NULL,
    identity_name varchar(128) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    active boolean DEFAULT true NOT NULL,
    CONSTRAINT manager_pkey PRIMARY KEY (manager_uuid),
    CONSTRAINT manager_identity FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE TABLE appcove_malawi.status (
    status_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    name varchar(128) NOT NULL,
    active boolean DEFAULT true NOT NULL,
    CONSTRAINT status_pkey PRIMARY KEY (status_uuid)
);

ALTER TABLE appcove_malawi.wkval
    ADD COLUMN status_uuid uuid,
    ADD COLUMN manager_uuid uuid,
    ADD COLUMN accomplishments text,
    ADD COLUMN percent_complete int,
    ADD COLUMN requirements_delivered_date date,
    ADD COLUMN requirements_reviewed_date date,
    ADD COLUMN value_delivered_date date,
    ADD COLUMN value_reviewed_date date,
    ADD COLUMN value_reviewed_by uuid;

ALTER TABLE appcove_malawi.wkval DROP COLUMN manager;
    
